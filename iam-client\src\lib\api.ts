import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api'

// Create axios instance
const api = axios.create({
	baseURL: API_BASE_URL,
	headers: {
		'Content-Type': 'application/json'
	}
})

// Request interceptor to add auth token
api.interceptors.request.use(
	(config) => {
		const token = localStorage.getItem('token')
		if (token) {
			config.headers.Authorization = `Bearer ${token}`
		}
		return config
	},
	(error) => {
		return Promise.reject(error)
	}
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
	(response) => response,
	(error) => {
		if (error.response?.status === 401) {
			localStorage.removeItem('token')
			localStorage.removeItem('user')
			window.location.href = '/login'
		}
		return Promise.reject(error)
	}
)

export default api

// API endpoints
export const authAPI = {
	login: (credentials: { username: string; password: string }) =>
		api.post('/auth/login', credentials),
	register: (userData: {
		username: string
		email: string
		password: string
		firstName: string
		lastName: string
	}) => api.post('/auth/register', userData),
	getProfile: () => api.get('/auth/me'),
	getPermissions: () => api.get('/auth/me/permissions'),
	simulateAction: (data: { module: string; action: string }) =>
		api.post('/auth/simulate-action', data)
}

export const usersAPI = {
	getAll: () => api.get('/users'),
	getById: (id: number) => api.get(`/users/${id}`),
	create: (userData: {
		username: string
		email: string
		password: string
		firstName: string
		lastName: string
	}) => api.post('/users', userData),
	update: (id: number, userData: Partial<{
		username: string
		email: string
		firstName: string
		lastName: string
	}>) => api.put(`/users/${id}`, userData),
	delete: (id: number) => api.delete(`/users/${id}`)
}

export const groupsAPI = {
	getAll: () => api.get('/groups'),
	getById: (id: number) => api.get(`/groups/${id}`),
	create: (groupData: { name: string; description?: string }) =>
		api.post('/groups', groupData),
	update: (id: number, groupData: Partial<{ name: string; description: string }>) =>
		api.put(`/groups/${id}`, groupData),
	delete: (id: number) => api.delete(`/groups/${id}`),
	assignUsers: (groupId: number, userIds: number[]) =>
		api.post(`/groups/${groupId}/users`, { userIds }),
	removeUsers: (groupId: number, userIds: number[]) =>
		api.delete(`/groups/${groupId}/users`, { data: { userIds } })
}

export const rolesAPI = {
	getAll: () => api.get('/roles'),
	getById: (id: number) => api.get(`/roles/${id}`),
	create: (roleData: { name: string; description?: string }) =>
		api.post('/roles', roleData),
	update: (id: number, roleData: Partial<{ name: string; description: string }>) =>
		api.put(`/roles/${id}`, roleData),
	delete: (id: number) => api.delete(`/roles/${id}`),
	assignToGroup: (groupId: number, roleIds: number[]) =>
		api.post(`/roles/groups/${groupId}/roles`, { roleIds }),
	removeFromGroup: (groupId: number, roleIds: number[]) =>
		api.delete(`/roles/groups/${groupId}/roles`, { data: { roleIds } })
}

export const modulesAPI = {
	getAll: () => api.get('/modules'),
	getById: (id: number) => api.get(`/modules/${id}`),
	create: (moduleData: { name: string; description?: string }) =>
		api.post('/modules', moduleData),
	update: (id: number, moduleData: Partial<{ name: string; description: string }>) =>
		api.put(`/modules/${id}`, moduleData),
	delete: (id: number) => api.delete(`/modules/${id}`)
}

export const permissionsAPI = {
	getAll: () => api.get('/permissions'),
	getById: (id: number) => api.get(`/permissions/${id}`),
	create: (permissionData: { action: string; moduleId: number }) =>
		api.post('/permissions', permissionData),
	update: (id: number, permissionData: Partial<{ action: string }>) =>
		api.put(`/permissions/${id}`, permissionData),
	delete: (id: number) => api.delete(`/permissions/${id}`),
	assignToRole: (roleId: number, permissionIds: number[]) =>
		api.post(`/permissions/roles/${roleId}/permissions`, { permissionIds }),
	removeFromRole: (roleId: number, permissionIds: number[]) =>
		api.delete(`/permissions/roles/${roleId}/permissions`, { data: { permissionIds } })
}
