import { useState } from 'react'
import { use<PERSON><PERSON>gate, <PERSON> } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../../hooks/redux'
import { login, clearError } from '../../store/authSlice'

function LoginForm() {
	const [credentials, setCredentials] = useState({
		username: '',
		password: ''
	})

	const dispatch = useAppDispatch()
	const navigate = useNavigate()
	const { isLoading, error } = useAppSelector((state) => state.auth)

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault()
		dispatch(clearError())
		
		const result = await dispatch(login(credentials))
		if (login.fulfilled.match(result)) {
			navigate('/dashboard')
		}
	}

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setCredentials({
			...credentials,
			[e.target.name]: e.target.value
		})
	}

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50">
			<div className="max-w-md w-full space-y-8">
				<div>
					<h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
						Sign in to IAM System
					</h2>
				</div>
				<form className="mt-8 space-y-6" onSubmit={handleSubmit}>
					{error && (
						<div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
							{error}
						</div>
					)}
					
					<div className="rounded-md shadow-sm -space-y-px">
						<div>
							<label htmlFor="username" className="sr-only">
								Username
							</label>
							<input
								id="username"
								name="username"
								type="text"
								required
								className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
								placeholder="Username"
								value={credentials.username}
								onChange={handleChange}
							/>
						</div>
						<div>
							<label htmlFor="password" className="sr-only">
								Password
							</label>
							<input
								id="password"
								name="password"
								type="password"
								required
								className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
								placeholder="Password"
								value={credentials.password}
								onChange={handleChange}
							/>
						</div>
					</div>

					<div>
						<button
							type="submit"
							disabled={isLoading}
							className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
						>
							{isLoading ? 'Signing in...' : 'Sign in'}
						</button>
					</div>

					<div className="text-center">
						<Link
							to="/register"
							className="font-medium text-indigo-600 hover:text-indigo-500"
						>
							Don't have an account? Sign up
						</Link>
					</div>
				</form>

				<div className="mt-6 p-4 bg-blue-50 rounded-md">
					<h3 className="text-sm font-medium text-blue-800 mb-2">Demo Accounts:</h3>
					<div className="text-xs text-blue-700 space-y-1">
						<div><strong>Admin:</strong> admin / admin123</div>
						<div><strong>Manager:</strong> manager / manager123</div>
						<div><strong>User:</strong> user / user123</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default LoginForm
