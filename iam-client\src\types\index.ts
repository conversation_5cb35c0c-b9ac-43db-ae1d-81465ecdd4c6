export interface User {
	id: number
	username: string
	email: string
	firstName: string
	lastName: string
	isActive: boolean
	createdAt: string
	updatedAt: string
	Groups?: Group[]
}

export interface Group {
	id: number
	name: string
	description?: string
	isActive: boolean
	createdAt: string
	updatedAt: string
	Users?: User[]
	Roles?: Role[]
}

export interface Role {
	id: number
	name: string
	description?: string
	isActive: boolean
	createdAt: string
	updatedAt: string
	Groups?: Group[]
	Permissions?: Permission[]
}

export interface Module {
	id: number
	name: string
	description?: string
	isActive: boolean
	createdAt: string
	updatedAt: string
	Permissions?: Permission[]
}

export interface Permission {
	id: number
	action: 'create' | 'read' | 'update' | 'delete'
	moduleId: number
	createdAt: string
	updatedAt: string
	Module?: Module
	Roles?: Role[]
}

export interface UserPermission {
	id: number
	action: string
	module: string
	moduleId: number
}

export interface AuthState {
	user: User | null
	token: string | null
	permissions: UserPermission[]
	isLoading: boolean
	error: string | null
}

export interface LoginCredentials {
	username: string
	password: string
}

export interface RegisterData {
	username: string
	email: string
	password: string
	firstName: string
	lastName: string
}

export interface ApiResponse<T> {
	data?: T
	message?: string
	error?: string
	details?: string[]
}

export interface PaginatedResponse<T> {
	data: T[]
	count: number
	page?: number
	limit?: number
	total?: number
}
