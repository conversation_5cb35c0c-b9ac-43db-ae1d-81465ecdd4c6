import { Link, useLocation } from 'react-router-dom'
import { useAppSelector } from '../../hooks/redux'

const navigation = [
	{ name: 'Dashboard', href: '/dashboard', icon: '📊' },
	{ name: 'Users', href: '/users', icon: '👥', permission: { module: 'Users', action: 'read' } },
	{ name: 'Groups', href: '/groups', icon: '👨‍👩‍👧‍👦', permission: { module: 'Groups', action: 'read' } },
	{ name: 'Roles', href: '/roles', icon: '🎭', permission: { module: 'Roles', action: 'read' } },
	{ name: 'Modules', href: '/modules', icon: '📦', permission: { module: 'Modules', action: 'read' } },
	{ name: 'Permissions', href: '/permissions', icon: '🔐', permission: { module: 'Permissions', action: 'read' } }
]

function Sidebar() {
	const location = useLocation()
	const { permissions } = useAppSelector((state) => state.auth)

	const hasPermission = (module: string, action: string) => {
		return permissions.some(p => p.module === module && p.action === action)
	}

	const filteredNavigation = navigation.filter(item => {
		if (!item.permission) return true
		return hasPermission(item.permission.module, item.permission.action)
	})

	return (
		<div className="flex flex-col w-64 bg-gray-800">
			<div className="flex items-center h-16 px-4 bg-gray-900">
				<h1 className="text-white text-lg font-semibold">IAM System</h1>
			</div>
			<nav className="flex-1 px-2 py-4 space-y-1">
				{filteredNavigation.map((item) => {
					const isActive = location.pathname === item.href
					return (
						<Link
							key={item.name}
							to={item.href}
							className={`${
								isActive
									? 'bg-gray-900 text-white'
									: 'text-gray-300 hover:bg-gray-700 hover:text-white'
							} group flex items-center px-2 py-2 text-sm font-medium rounded-md`}
						>
							<span className="mr-3 text-lg">{item.icon}</span>
							{item.name}
						</Link>
					)
				})}
			</nav>
		</div>
	)
}

export default Sidebar
