import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { authAPI } from '../lib/api'
import { AuthState, LoginCredentials, RegisterData, User, UserPermission } from '../types'

const initialState: AuthState = {
	user: null,
	token: localStorage.getItem('token'),
	permissions: [],
	isLoading: false,
	error: null
}

// Async thunks
export const login = createAsyncThunk('auth/login', async (credentials: LoginCredentials, { rejectWithValue }) => {
	try {
		const response = await authAPI.login(credentials)
		const { user, token } = response.data
		localStorage.setItem('token', token)
		localStorage.setItem('user', JSON.stringify(user))
		return { user, token }
	} catch (error: unknown) {
		return rejectWithValue(error.response?.data?.error || 'Login failed')
	}
})

export const register = createAsyncThunk('auth/register', async (userData: RegisterData, { rejectWithValue }) => {
	try {
		const response = await authAPI.register(userData)
		const { user, token } = response.data
		localStorage.setItem('token', token)
		localStorage.setItem('user', JSON.stringify(user))
		return { user, token }
	} catch (error: any) {
		return rejectWithValue(error.response?.data?.error || 'Registration failed')
	}
})

export const getProfile = createAsyncThunk('auth/getProfile', async (_, { rejectWithValue }) => {
	try {
		const response = await authAPI.getProfile()
		return response.data.user
	} catch (error: any) {
		return rejectWithValue(error.response?.data?.error || 'Failed to get profile')
	}
})

export const getPermissions = createAsyncThunk('auth/getPermissions', async (_, { rejectWithValue }) => {
	try {
		const response = await authAPI.getPermissions()
		return response.data.permissions
	} catch (error: any) {
		return rejectWithValue(error.response?.data?.error || 'Failed to get permissions')
	}
})

export const simulateAction = createAsyncThunk(
	'auth/simulateAction',
	async (data: { module: string; action: string }, { rejectWithValue }) => {
		try {
			const response = await authAPI.simulateAction(data)
			return response.data
		} catch (error: any) {
			return rejectWithValue(error.response?.data?.error || 'Failed to simulate action')
		}
	}
)

const authSlice = createSlice({
	name: 'auth',
	initialState,
	reducers: {
		logout: state => {
			state.user = null
			state.token = null
			state.permissions = []
			state.error = null
			localStorage.removeItem('token')
			localStorage.removeItem('user')
		},
		clearError: state => {
			state.error = null
		},
		loadUserFromStorage: state => {
			const storedUser = localStorage.getItem('user')
			if (storedUser) {
				state.user = JSON.parse(storedUser)
			}
		}
	},
	extraReducers: builder => {
		// Login
		builder
			.addCase(login.pending, state => {
				state.isLoading = true
				state.error = null
			})
			.addCase(login.fulfilled, (state, action) => {
				state.isLoading = false
				state.user = action.payload.user
				state.token = action.payload.token
				state.error = null
			})
			.addCase(login.rejected, (state, action) => {
				state.isLoading = false
				state.error = action.payload as string
			})

		// Register
		builder
			.addCase(register.pending, state => {
				state.isLoading = true
				state.error = null
			})
			.addCase(register.fulfilled, (state, action) => {
				state.isLoading = false
				state.user = action.payload.user
				state.token = action.payload.token
				state.error = null
			})
			.addCase(register.rejected, (state, action) => {
				state.isLoading = false
				state.error = action.payload as string
			})

		// Get Profile
		builder
			.addCase(getProfile.pending, state => {
				state.isLoading = true
			})
			.addCase(getProfile.fulfilled, (state, action) => {
				state.isLoading = false
				state.user = action.payload
				localStorage.setItem('user', JSON.stringify(action.payload))
			})
			.addCase(getProfile.rejected, (state, action) => {
				state.isLoading = false
				state.error = action.payload as string
			})

		// Get Permissions
		builder
			.addCase(getPermissions.pending, state => {
				state.isLoading = true
			})
			.addCase(getPermissions.fulfilled, (state, action) => {
				state.isLoading = false
				state.permissions = action.payload
			})
			.addCase(getPermissions.rejected, (state, action) => {
				state.isLoading = false
				state.error = action.payload as string
			})
	}
})

export const { logout, clearError, loadUserFromStorage } = authSlice.actions
export default authSlice.reducer
