import { useEffect } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAppSelector, useAppDispatch } from '../../hooks/redux'
import { loadUserFromStorage, getPermissions } from '../../store/authSlice'

interface ProtectedRouteProps {
	children: React.ReactNode
}

function ProtectedRoute({ children }: ProtectedRouteProps) {
	const { user, token } = useAppSelector((state) => state.auth)
	const dispatch = useAppDispatch()
	const location = useLocation()

	useEffect(() => {
		if (token && !user) {
			dispatch(loadUserFromStorage())
		}
		if (token && user) {
			dispatch(getPermissions())
		}
	}, [token, user, dispatch])

	if (!token) {
		return <Navigate to="/login" state={{ from: location }} replace />
	}

	return <>{children}</>
}

export default ProtectedRoute
