import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Provider } from 'react-redux'
import { store } from './store'
import ProtectedRoute from './components/layout/ProtectedRoute'
import Layout from './components/layout/Layout'
import LoginForm from './components/auth/LoginForm'
import RegisterForm from './components/auth/RegisterForm'
import Dashboard from './components/dashboard/Dashboard'
import './App.css'

function App() {
	return (
		<Provider store={store}>
			<Router>
				<Routes>
					<Route path="/login" element={<LoginForm />} />
					<Route path="/register" element={<RegisterForm />} />
					<Route
						path="/"
						element={
							<ProtectedRoute>
								<Layout />
							</ProtectedRoute>
						}
					>
						<Route index element={<Navigate to="/dashboard" replace />} />
						<Route path="dashboard" element={<Dashboard />} />
						<Route path="users" element={<div>Users Management (Coming Soon)</div>} />
						<Route path="groups" element={<div>Groups Management (Coming Soon)</div>} />
						<Route path="roles" element={<div>Roles Management (Coming Soon)</div>} />
						<Route path="modules" element={<div>Modules Management (Coming Soon)</div>} />
						<Route path="permissions" element={<div>Permissions Management (Coming Soon)</div>} />
					</Route>
				</Routes>
			</Router>
		</Provider>
	)
}

export default App
