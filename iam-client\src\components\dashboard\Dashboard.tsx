import { useState, useEffect } from 'react'
import { useAppSelector, useAppDispatch } from '../../hooks/redux'
import { simulateAction } from '../../store/authSlice'

function Dashboard() {
	const { user, permissions } = useAppSelector((state) => state.auth)
	const dispatch = useAppDispatch()
	
	const [simulation, setSimulation] = useState({
		module: '',
		action: '',
		result: null as any
	})

	const modules = ['Users', 'Groups', 'Roles', 'Modules', 'Permissions']
	const actions = ['create', 'read', 'update', 'delete']

	const handleSimulate = async () => {
		if (simulation.module && simulation.action) {
			const result = await dispatch(simulateAction({
				module: simulation.module,
				action: simulation.action
			}))
			setSimulation(prev => ({ ...prev, result: result.payload }))
		}
	}

	const groupedPermissions = permissions.reduce((acc, permission) => {
		if (!acc[permission.module]) {
			acc[permission.module] = []
		}
		acc[permission.module].push(permission.action)
		return acc
	}, {} as Record<string, string[]>)

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
				<p className="mt-1 text-sm text-gray-600">
					Welcome to the IAM System. Here you can view your permissions and simulate actions.
				</p>
			</div>

			{/* User Info */}
			<div className="bg-white overflow-hidden shadow rounded-lg">
				<div className="px-4 py-5 sm:p-6">
					<h3 className="text-lg leading-6 font-medium text-gray-900">User Information</h3>
					<div className="mt-2 max-w-xl text-sm text-gray-500">
						<p>Your current user details and account status.</p>
					</div>
					<div className="mt-3">
						<div className="grid grid-cols-2 gap-4">
							<div>
								<dt className="text-sm font-medium text-gray-500">Full Name</dt>
								<dd className="mt-1 text-sm text-gray-900">{user?.firstName} {user?.lastName}</dd>
							</div>
							<div>
								<dt className="text-sm font-medium text-gray-500">Username</dt>
								<dd className="mt-1 text-sm text-gray-900">{user?.username}</dd>
							</div>
							<div>
								<dt className="text-sm font-medium text-gray-500">Email</dt>
								<dd className="mt-1 text-sm text-gray-900">{user?.email}</dd>
							</div>
							<div>
								<dt className="text-sm font-medium text-gray-500">Status</dt>
								<dd className="mt-1 text-sm text-gray-900">
									<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
										Active
									</span>
								</dd>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Permissions */}
			<div className="bg-white overflow-hidden shadow rounded-lg">
				<div className="px-4 py-5 sm:p-6">
					<h3 className="text-lg leading-6 font-medium text-gray-900">Your Permissions</h3>
					<div className="mt-2 max-w-xl text-sm text-gray-500">
						<p>Permissions you have inherited through your group memberships.</p>
					</div>
					<div className="mt-4">
						{Object.keys(groupedPermissions).length === 0 ? (
							<p className="text-sm text-gray-500">No permissions assigned.</p>
						) : (
							<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
								{Object.entries(groupedPermissions).map(([module, moduleActions]) => (
									<div key={module} className="border border-gray-200 rounded-lg p-4">
										<h4 className="font-medium text-gray-900">{module}</h4>
										<div className="mt-2 flex flex-wrap gap-1">
											{moduleActions.map((action) => (
												<span
													key={action}
													className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
												>
													{action}
												</span>
											))}
										</div>
									</div>
								))}
							</div>
						)}
					</div>
				</div>
			</div>

			{/* Action Simulator */}
			<div className="bg-white overflow-hidden shadow rounded-lg">
				<div className="px-4 py-5 sm:p-6">
					<h3 className="text-lg leading-6 font-medium text-gray-900">Action Simulator</h3>
					<div className="mt-2 max-w-xl text-sm text-gray-500">
						<p>Test whether you have permission to perform specific actions on modules.</p>
					</div>
					<div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
						<div>
							<label htmlFor="module" className="block text-sm font-medium text-gray-700">
								Module
							</label>
							<select
								id="module"
								value={simulation.module}
								onChange={(e) => setSimulation(prev => ({ ...prev, module: e.target.value, result: null }))}
								className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
							>
								<option value="">Select module</option>
								{modules.map((module) => (
									<option key={module} value={module}>{module}</option>
								))}
							</select>
						</div>
						<div>
							<label htmlFor="action" className="block text-sm font-medium text-gray-700">
								Action
							</label>
							<select
								id="action"
								value={simulation.action}
								onChange={(e) => setSimulation(prev => ({ ...prev, action: e.target.value, result: null }))}
								className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
							>
								<option value="">Select action</option>
								{actions.map((action) => (
									<option key={action} value={action}>{action}</option>
								))}
							</select>
						</div>
						<div className="flex items-end">
							<button
								onClick={handleSimulate}
								disabled={!simulation.module || !simulation.action}
								className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-300 text-white px-4 py-2 rounded-md text-sm font-medium"
							>
								Test Permission
							</button>
						</div>
					</div>
					{simulation.result && (
						<div className={`mt-4 p-4 rounded-md ${simulation.result.allowed ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
							<div className={`text-sm ${simulation.result.allowed ? 'text-green-800' : 'text-red-800'}`}>
								<strong>{simulation.result.allowed ? '✅ Allowed' : '❌ Denied'}</strong>
								<p className="mt-1">{simulation.result.message}</p>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	)
}

export default Dashboard
