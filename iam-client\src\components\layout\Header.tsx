import { useAppSelector, useAppDispatch } from '../../hooks/redux'
import { logout } from '../../store/authSlice'

function Header() {
	const { user } = useAppSelector((state) => state.auth)
	const dispatch = useAppDispatch()

	const handleLogout = () => {
		dispatch(logout())
	}

	return (
		<header className="bg-white shadow-sm border-b border-gray-200">
			<div className="flex items-center justify-between h-16 px-6">
				<div className="flex items-center">
					<h2 className="text-xl font-semibold text-gray-800">
						Welcome, {user?.firstName} {user?.lastName}
					</h2>
				</div>
				<div className="flex items-center space-x-4">
					<div className="text-sm text-gray-600">
						{user?.username} ({user?.email})
					</div>
					<button
						onClick={handleLogout}
						className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
					>
						Logout
					</button>
				</div>
			</div>
		</header>
	)
}

export default Header
